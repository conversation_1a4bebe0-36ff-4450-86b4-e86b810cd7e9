<?php

namespace app\modules\backend\controllers;

use Yii;
use yii\web\Response;
use yii\data\Pagination;
use app\common\models\Client;
use app\common\models\ClientBalance;
use app\common\models\ClientBalanceHistory;
use app\modules\backend\models\ClientBalanceAdjustmentForm;

class ClientBalanceAdjustmentController extends BaseController
{
    public function actionIndex()
    {
        $search = Yii::$app->request->get('search', '');
        $balanceFilter = Yii::$app->request->get('balance_filter', '');

        $query = Client::find()
            ->select([
                'client.*',
                'region.name as region_name',
                'COALESCE(client_balance.amount, 0) as balance'
            ])
            ->leftJoin('region', 'client.region_id = region.id')
            ->leftJoin('client_balance', 'client.id = client_balance.client_id')
            ->where(['client.deleted_at' => null]);

        // Добавляем поиск по имени клиента или телефону
        if (!empty($search)) {
            $query->andWhere([
                'or',
                ['ilike', 'client.full_name', $search],
                ['ilike', 'client.phone_number', $search],
                ['ilike', 'client.phone_number_2', $search],
                ['ilike', 'region.name', $search]
            ]);
        }

        // Добавляем фильтр по типу баланса
        if (!empty($balanceFilter)) {
            switch ($balanceFilter) {
                case 'debt':
                    $query->andWhere(['>', 'client_balance.amount', 0]);
                    break;
                case 'prepayment':
                    $query->andWhere(['<', 'client_balance.amount', 0]);
                    break;
                case 'no_balance':
                    $query->andWhere([
                        'or',
                        ['client_balance.amount' => 0],
                        ['client_balance.amount' => null]
                    ]);
                    break;
            }
        }

        $query->orderBy(['client.full_name' => SORT_ASC]);

        $pagination = new Pagination([
            'defaultPageSize' => 50,
            'totalCount' => $query->count(),
        ]);

        $clients = $query->offset($pagination->offset)
            ->limit($pagination->limit)
            ->asArray()
            ->all();

        return $this->render('index', [
            'clients' => $clients,
            'pagination' => $pagination,
            'search' => $search,
            'balanceFilter' => $balanceFilter,
        ]);
    }

    public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $model = new ClientBalanceAdjustmentForm();

            if (!$model->load($data)) {
                return [
                    'status' => 'error',
                    'errors' => ['Не удалось загрузить данные.']
                ];
            }

            if (!$model->validate()) {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors(),
                ];
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                // Найдем клиента
                $client = Client::findOne($model->client_id);
                if (!$client) {
                    throw new \Exception('Клиент не найден');
                }

                // Найдем текущий баланс клиента
                $clientBalance = ClientBalance::findOne(['client_id' => $model->client_id]);
                if (!$clientBalance) {
                    $clientBalance = new ClientBalance();
                    $clientBalance->client_id = $model->client_id;
                    $clientBalance->amount = 0;
                }

                $oldAmount = $clientBalance->amount;
                $newAmount = $model->amount;

                // Определим тип операции и новую сумму
                if ($model->balance_type == 1) { // долг
                    $operationType = 'debt_adjustment';
                    $newAmount = abs($newAmount);
                } elseif ($model->balance_type == 2) { // предоплата
                    $operationType = 'prepayment_adjustment';
                    $newAmount = -abs($newAmount);
                } elseif ($model->balance_type == 3) { // сброс баланса
                    $operationType = 'balance_reset';
                    $newAmount = 0;
                }

                // Обновим баланс
                $clientBalance->amount = $newAmount;
                $clientBalance->updated_at = date('Y-m-d H:i:s');

                if (!$clientBalance->save()) {
                    throw new \Exception('Ошибка при сохранении баланса: ' . json_encode($clientBalance->getErrors()));
                }

                // Записываем в историю
                $history = new ClientBalanceHistory();
                $history->client_id = $model->client_id;
                $history->amount = $newAmount;
                $history->old_amount = $oldAmount;
                $history->type = $operationType;
                $history->created_at = date('Y-m-d H:i:s');

                if (!$history->save()) {
                    throw new \Exception('Ошибка при записи в историю: ' . json_encode($history->getErrors()));
                }

                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'client_balance_successfully_updated'),
                ];

            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage(),
                ];
            }

        } elseif (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $client = Client::find()
                ->select([
                    'client.*',
                    'COALESCE(client_balance.amount, 0) as current_balance'
                ])
                ->leftJoin('client_balance', 'client.id = client_balance.client_id')
                ->where(['client.id' => $id])
                ->asArray()
                ->one();

            if (!$client) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'client_not_found'),
                ];
            }

            $model = new ClientBalanceAdjustmentForm();
            $model->client_id = $id;

            return [
                'status' => 'success',
                'content' => $this->renderPartial('update', [
                    'model' => $model,
                    'client' => $client,
                ])
            ];
        }

        return [
            'status' => 'error',
            'message' => 'Неподдерживаемый метод запроса.',
        ];
    }
} 