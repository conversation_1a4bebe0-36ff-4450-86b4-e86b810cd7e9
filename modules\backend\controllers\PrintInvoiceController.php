<?php

namespace app\modules\backend\controllers;

use app\common\models\Sales;
use app\common\models\SalesDetail;
use Exception;
use Yii;

class PrintInvoiceController extends BaseController
{
    /**
     * Показать печатную накладную
     */
    public function actionView($id)
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        try {
            // Получаем данные по накладной
            $sales = Sales::find()
                ->with([
                    'client',
                    'salesDetails' => function($query) {
                        $query->with('product')->andWhere(['deleted_at' => null]);
                    },
                    'bonus' => function($query) {
                        $query->with('product')->andWhere(['deleted_at' => null]);
                    }
                ])
                ->where(['id' => $id])
                ->one();

            if (!$sales) {
                return [
                    'status' => 'error',
                    'message' => 'Накладная не найдена'
                ];
            }

            // Автоматически предлагаем следующий доступный номер корешка
            $printNumber = $this->getNextPrintNumber();

            // Подготавливаем данные для отображения
            $printData = $this->preparePrintData($sales);

            return [
                'status' => 'success',
                'content' => $this->renderPartial('view', [
                    'sales' => $sales,
                    'printNumber' => $printNumber,
                    'printData' => $printData
                ])
            ];

        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Подготовить данные для печатной накладной
     */
    private function preparePrintData($sales)
    {
        // Статические цены (как на втором фото)
        $staticPrices = [
            '0.33' => [
                '1-narx' => 950,
                '2-narx' => 950,
                '3-narx' => 950,
                '4-narx' => 950,
                '5-narx' => 975
            ],
            '0.5 gaz' => [
                '1-narx' => 990,
                '2-narx' => 1100,
                '3-narx' => 1100,
                '4-narx' => 1100,
                '5-narx' => 1175
            ],
            '0.5 bezgaz' => [
                '1-narx' => 990,
                '2-narx' => 1100,
                '3-narx' => 1100,
                '4-narx' => 1100,
                '5-narx' => 1175
            ],
            '1 gaz' => [
                '1-narx' => 1350,
                '2-narx' => 1500,
                '3-narx' => 1500,
                '4-narx' => 1500,
                '5-narx' => 1575
            ],
            '1 bezgaz' => [
                '1-narx' => 1350,
                '2-narx' => 1500,
                '3-narx' => 1500,
                '4-narx' => 1500,
                '5-narx' => 1575
            ],
            '1.5 gaz' => [
                '1-narx' => 1665,
                '2-narx' => 1850,
                '3-narx' => 1850,
                '4-narx' => 1850,
                '5-narx' => 1925
            ],
            '1.5 bezgaz' => [
                '1-narx' => 1665,
                '2-narx' => 1850,
                '3-narx' => 1850,
                '4-narx' => 1850,
                '5-narx' => 1925
            ],
            '5' => [
                '1-narx' => 3500,
                '2-narx' => 3800,
                '3-narx' => 4000,
                '4-narx' => 3500,
                '5-narx' => 4400
            ],
            '10' => [
                '1-narx' => 5000,
                '2-narx' => 5500,
                '3-narx' => 6000,
                '4-narx' => 5000,
                '5-narx' => 6500
            ]
        ];

        $printItems = [];
        $bonusItems = [];
        $totalWeight = 0;

        // Обрабатываем обычные продукты
        foreach ($sales->salesDetails as $detail) {
            $productName = $detail->product->name;
            $quantity = $detail->quantity;
            $size = $detail->product->size ?? 1;
            $blocks = $size > 0 ? round($quantity / $size, 1) : 0;
            
            // Определяем тип продукта по названию
            $printType = $this->matchProductToPrintType($productName);
            
            if ($printType) {
                if (!isset($printItems[$printType])) {
                    $printItems[$printType] = [
                        'blocks' => 0,
                        'quantity' => 0,
                        'weight' => $this->getProductWeight($printType)
                    ];
                }
                
                $printItems[$printType]['blocks'] += $blocks;
                $printItems[$printType]['quantity'] += $quantity;
                $totalWeight += $quantity * $this->getProductWeight($printType);
            }
        }

        // Обрабатываем бонусные продукты отдельно
        foreach ($sales->bonus as $bonus) {
            $productName = $bonus->product->name;
            $quantity = $bonus->quantity;
            $size = $bonus->product->size ?? 1;
            $blocks = $size > 0 ? round($quantity / $size, 1) : 0;
            
            // Определяем тип продукта по названию
            $printType = $this->matchProductToPrintType($productName);
            
            if ($printType) {
                if (!isset($bonusItems[$printType])) {
                    $bonusItems[$printType] = [
                        'blocks' => 0,
                        'quantity' => 0,
                        'weight' => $this->getProductWeight($printType)
                    ];
                }
                
                $bonusItems[$printType]['blocks'] += $blocks;
                $bonusItems[$printType]['quantity'] += $quantity;
                $totalWeight += $quantity * $this->getProductWeight($printType);
            }
        }

        return [
            'items' => $printItems,
            'bonusItems' => $bonusItems,
            'totalWeight' => $totalWeight,
            'prices' => $staticPrices
        ];
    }

    /**
     * Сопоставление названия продукта с типом для печати
     */
    private function matchProductToPrintType($productName)
    {
        $productName = mb_strtolower(trim($productName));
        
        // Правила сопоставления
        if (strpos($productName, '0.33') !== false || strpos($productName, '0,33') !== false) {
            return '0.33';
        }
        
        // 0.5 литра - улучшенное распознавание для узбекских названий
        if (strpos($productName, '0.5') !== false || strpos($productName, '0,5') !== false) {
            // Проверяем на газированность - ищем явные указания на газированность
            if (strpos($productName, 'газланган') !== false || 
                strpos($productName, 'gazlangan') !== false ||
                strpos($productName, 'газли') !== false ||
                strpos($productName, 'gazli') !== false) {
                return '0.5 gaz';
            }
            // Проверяем на безгазовость
            else if (strpos($productName, 'безгаз') !== false || 
                     strpos($productName, 'bezgaz') !== false ||
                     strpos($productName, 'без газ') !== false) {
                return '0.5 bezgaz';
            }
            // По умолчанию считаем безгазовым если не указано обратное
            else {
                return '0.5 bezgaz';
            }
        }
        
        // 1.5 литра - улучшенное распознавание
        if (strpos($productName, '1.5') !== false || strpos($productName, '1,5') !== false || strpos($productName, '1.75') !== false) {
            // Проверяем на газированность - ищем явные указания на газированность
            if (strpos($productName, 'газланган') !== false || 
                strpos($productName, 'gazlangan') !== false ||
                strpos($productName, 'газли') !== false ||
                strpos($productName, 'gazli') !== false) {
                return '1.5 gaz';
            }
            // Проверяем на безгазовость
            else if (strpos($productName, 'безгаз') !== false || 
                     strpos($productName, 'bezgaz') !== false ||
                     strpos($productName, 'без газ') !== false) {
                return '1.5 bezgaz';
            }
            // По умолчанию считаем безгазовым если не указано обратное
            else {
                return '1.5 bezgaz';
            }
        }
        
        // 1 литр и Palmyra - улучшенное распознавание
        if (strpos($productName, '1.0') !== false || strpos($productName, '1,0') !== false || 
            (strpos($productName, '1 ') !== false && strpos($productName, '1.5') === false) ||
            strpos($productName, '1 litr') !== false ||
            (strpos($productName, 'palmyra') !== false && strpos($productName, '1.75') === false && strpos($productName, '55') === false)) {
            // Проверяем на газированность - ищем явные указания на газированность
            if (strpos($productName, 'газланган') !== false || 
                strpos($productName, 'gazlangan') !== false ||
                strpos($productName, 'газли') !== false ||
                strpos($productName, 'gazli') !== false) {
                return '1 gaz';
            }
            // Проверяем на безгазовость
            else if (strpos($productName, 'безгаз') !== false || 
                     strpos($productName, 'bezgaz') !== false ||
                     strpos($productName, 'без газ') !== false) {
                return '1 bezgaz';
            }
            // По умолчанию считаем безгазовым если не указано обратное
            else {
                return '1 bezgaz';
            }
        }
        
      
        // 5 литров
        if (strpos($productName, '5') !== false) {
            return '5';
        }
        
        // 10 литров
        if (strpos($productName, '10') !== false) {
            return '10';
        }
        
        return null;
    }

    /**
     * Получить вес продукта (в кг)
     */
    private function getProductWeight($printType)
    {
        $weights = [
            '0.33' => 0.33,
            '0.5 gaz' => 0.5,
            '0.5 bezgaz' => 0.5,
            '1 gaz' => 1.0,
            '1 bezgaz' => 1.0,
            '1.5 gaz' => 1.5,
            '1.5 bezgaz' => 1.5,
            '5' => 5.0,
            '10' => 10.0
        ];
        
        return $weights[$printType] ?? 0;
    }


    /**
     * Получает следующий доступный номер корешка
     * Ищет максимальный номер за текущий день, если не найден - ищет за предыдущие дни
     * @return int
     */
    private function getNextPrintNumber()
    {
        // Сначала ищем максимальный номер за сегодня
        $today = date('Y-m-d');
        $maxNumber = Sales::find()
            ->where(['not', ['print_number' => null]])
            ->andWhere(['not', ['print_number' => '']])
            ->andWhere(['not', ['print_number' => '0']])
            ->andWhere(['>=', 'created_at', $today . ' 00:00:00'])
            ->andWhere(['<=', 'created_at', $today . ' 23:59:59'])
            ->max('CAST(print_number AS UNSIGNED)');

        if ($maxNumber !== null) {
            return (int)$maxNumber + 1;
        }

        // Если за сегодня нет записей, ищем максимальный номер за предыдущие дни
        // Начинаем с вчерашнего дня и идем назад до 7 дней
        for ($i = 1; $i <= 7; $i++) {
            $checkDate = date('Y-m-d', strtotime("-{$i} days"));
            $maxNumber = Sales::find()
                ->where(['not', ['print_number' => null]])
                ->andWhere(['not', ['print_number' => '']])
                ->andWhere(['not', ['print_number' => '0']])
                ->andWhere(['>=', 'created_at', $checkDate . ' 00:00:00'])
                ->andWhere(['<=', 'created_at', $checkDate . ' 23:59:59'])
                ->max('CAST(print_number AS UNSIGNED)');

            if ($maxNumber !== null) {
                return (int)$maxNumber + 1;
            }
        }

        // Если ничего не найдено, начинаем с 1
        return 1;
    }

    /**
     * Проверяет уникальность номера корешка за текущий день
     * @param string $printNumber
     * @param int|null $excludeId ID записи, которую нужно исключить из проверки
     * @return bool
     */
    private function isPrintNumberUnique($printNumber, $excludeId = null)
    {
        $today = date('Y-m-d');
        $query = Sales::find()
            ->where(['print_number' => $printNumber])
            ->andWhere(['>=', 'created_at', $today . ' 00:00:00'])
            ->andWhere(['<=', 'created_at', $today . ' 23:59:59']);

        if ($excludeId !== null) {
            $query->andWhere(['!=', 'id', $excludeId]);
        }

        return $query->count() === 0;
    }

    public function actionSavePrintNumber()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $id = Yii::$app->request->post('id');
        $printNumber = Yii::$app->request->post('print_number');

        // Проверяем, что номер корешка не пустой
        if (empty($printNumber) || $printNumber == '0' || trim($printNumber) === '') {
            return [
                'success' => false,
                'message' => 'Номер корешка не может быть пустым'
            ];
        }

        // Проверяем уникальность номера корешка за текущий день
        if (!$this->isPrintNumberUnique($printNumber, $id)) {
            $nextNumber = $this->getNextPrintNumber();
            return [
                'success' => false,
                'message' => "Номер корешка {$printNumber} уже используется сегодня. Предлагаемый номер: {$nextNumber}",
                'suggested_number' => $nextNumber
            ];
        }

        $sales = Sales::findOne($id);
        if (!$sales) {
            return [
                'success' => false,
                'message' => 'Накладная не найдена'
            ];
        }

        $sales->print_number = $printNumber;
        if ($sales->save(false)) {
            return [
                'success' => true,
                'message' => 'Номер корешка сохранен'
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Ошибка при сохранении номера корешка'
            ];
        }
    }

    
} 