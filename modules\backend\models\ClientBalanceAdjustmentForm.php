<?php

namespace app\modules\backend\models;

use Yii;
use yii\base\Model;
use app\common\models\Client;

/**
 * Форма для корректировки баланса клиента
 */
class ClientBalanceAdjustmentForm extends Model
{
    public $client_id;
    public $amount;
    public $balance_type; // 1 = долг, 2 = предоплата

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['client_id', 'balance_type'], 'required'],
            [['amount'], 'required', 'when' => function($model) {
                return $model->balance_type != 3; // Если не сброс баланса
            }],
            [['client_id', 'balance_type'], 'integer'],
            [['amount'], 'number', 'min' => 0],
            [['balance_type'], 'in', 'range' => [1, 2, 3]], // Добавляем тип "сброс баланса"
            [['client_id'], 'exist', 'targetClass' => Client::class, 'targetAttribute' => 'id'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'client_id' => Yii::t('app', 'client'),
            'amount' => Yii::t('app', 'amount'),
            'balance_type' => Yii::t('app', 'balance_type'),
        ];
    }

    /**
     * Получить список типов баланса
     */
    public static function getBalanceTypes()
    {
        return [
            1 => Yii::t('app', 'debt'),
            2 => Yii::t('app', 'prepayment'),
            3 => Yii::t('app', 'reset_balance'), // Сброс баланса к 0
        ];
    }

    /**
     * Получить название типа баланса
     */
    public function getBalanceTypeName()
    {
        $types = self::getBalanceTypes();
        return isset($types[$this->balance_type]) ? $types[$this->balance_type] : '';
    }

    /**
     * Перед валидацией очищаем форматированные числовые поля от пробелов
     * @return bool
     */
    public function beforeValidate()
    {
        if (is_string($this->amount) && !empty($this->amount)) {
            $originalValue = $this->amount;
            
            $cleanValue = preg_replace('/[\s\xA0]+/u', '', $originalValue);
            $cleanValue = str_replace(',', '.', $cleanValue);
            
            if (is_numeric($cleanValue)) {
                $this->amount = floatval($cleanValue);
            }
        }
        
        return parent::beforeValidate();
    }
} 