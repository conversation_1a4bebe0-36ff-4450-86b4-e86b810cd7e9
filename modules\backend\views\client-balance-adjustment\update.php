<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use app\modules\backend\models\ClientBalanceAdjustmentForm;

/* @var $this yii\web\View */
/* @var $model ClientBalanceAdjustmentForm */
/* @var $client array */

?>

<div class="modal-header">
    <h5 class="modal-title"><?= Yii::t('app', 'adjust_client_balance') ?></h5>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>

<div class="modal-body">
    <div class="form-errors"></div>
    
    <!-- Информация о клиенте -->
    <div class="card mb-3">
        <div class="card-body">
            <h6 class="card-title"><?= Yii::t('app', 'client_information') ?></h6>
            <div class="row">
                <div class="col-md-6">
                    <p><strong><?= Yii::t('app', 'client_name') ?>:</strong> <?= Html::encode($client['full_name']) ?></p>
                </div>
                <div class="col-md-6">
                    <p><strong><?= Yii::t('app', 'current_balance') ?>:</strong> 
                        <?php 
                            $currentBalance = (float)$client['current_balance'];
                            $balanceClass = $currentBalance > 0 ? 'text-danger' : ($currentBalance < 0 ? 'text-success' : 'text-muted');
                        ?>
                        <span class="<?= $balanceClass ?>">
                            <?= number_format(abs($currentBalance), 2, '.', ' ') ?>
                            <?php if ($currentBalance > 0): ?>
                                (<?= Yii::t('app', 'debt') ?>)
                            <?php elseif ($currentBalance < 0): ?>
                                (<?= Yii::t('app', 'prepayment') ?>)
                            <?php else: ?>
                                (<?= Yii::t('app', 'no_balance') ?>)
                            <?php endif; ?>
                        </span>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <?php $form = ActiveForm::begin([
        'id' => 'balance-adjustment-form',
        'options' => ['class' => 'form-horizontal'],
        'fieldConfig' => [
            'template' => '<div class="form-group row">{label}<div class="col-sm-8">{input}{error}</div></div>',
            'labelOptions' => ['class' => 'col-sm-4 col-form-label'],
        ],
    ]); ?>

    <?= Html::hiddenInput('ClientBalanceAdjustmentForm[client_id]', $model->client_id) ?>

    <div class="form-group row">
        <label class="col-sm-4 col-form-label"><?= Yii::t('app', 'balance_type') ?></label>
        <div class="col-sm-8">
            <?= Html::dropDownList(
                'ClientBalanceAdjustmentForm[balance_type]',
                $model->balance_type,
                ClientBalanceAdjustmentForm::getBalanceTypes(),
                [
                    'class' => 'form-control',
                    'prompt' => Yii::t('app', 'select_balance_type'),
                    'required' => true,
                    'id' => 'balance-type-select'
                ]
            ) ?>
        </div>
    </div>

    <div class="form-group row" id="amount-field">
        <label class="col-sm-4 col-form-label"><?= Yii::t('app', 'amount') ?></label>
        <div class="col-sm-8">
            <?= Html::textInput(
                'ClientBalanceAdjustmentForm[amount]',
                $model->amount,
                [
                    'class' => 'form-control',
                    'placeholder' => Yii::t('app', 'enter_amount'),
                    'step' => '0.01',
                    'min' => '0',
                    'id' => 'amount-input'
                ]
            ) ?>
            <small class="form-text text-muted" id="amount-help">
                <?= Yii::t('app', 'enter_amount_greater_than_zero') ?>
            </small>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const balanceTypeSelect = document.getElementById('balance-type-select');
        const amountField = document.getElementById('amount-field');
        const amountInput = document.getElementById('amount-input');
        const amountHelp = document.getElementById('amount-help');

        function toggleAmountField() {
            const selectedType = balanceTypeSelect.value;
            
            if (selectedType === '3') { // Сброс баланса
                amountField.style.display = 'none';
                amountInput.removeAttribute('required');
                amountInput.value = '0';
            } else {
                amountField.style.display = 'block';
                amountInput.setAttribute('required', 'required');
                if (selectedType === '1') { // Долг
                    amountHelp.textContent = '<?= Yii::t('app', 'enter_debt_amount') ?>';
                } else if (selectedType === '2') { // Предоплата
                    amountHelp.textContent = '<?= Yii::t('app', 'enter_prepayment_amount') ?>';
                }
            }
        }

        balanceTypeSelect.addEventListener('change', toggleAmountField);
        
        // Инициализация при загрузке страницы
        toggleAmountField();
    });
    </script>

    <?php ActiveForm::end(); ?>
</div>

<div class="modal-footer">
    <button type="button" class="btn btn-secondary" data-dismiss="modal">
        <?= Yii::t('app', 'cancel') ?>
    </button>
    <button type="submit" form="balance-adjustment-form" class="btn btn-primary">
        <?= Yii::t('app', 'save_changes') ?>
    </button>
</div> 